#!/usr/bin/env python3
"""
Test script for PDF Splitter application.

This script creates a sample multi-page PDF for testing purposes and then
tests the PDF splitter functionality.
"""

import os
import sys
import tempfile
from pathlib import Path

try:
    from pypdf import PdfWriter
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
except ImportError:
    print("Missing dependencies for testing.")
    print("Please install: pip install pypdf reportlab")
    sys.exit(1)

from pdf_splitter import PDFSplitter, PDFSplitterError


def create_sample_pdf(filename: str, num_pages: int = 5) -> Path:
    """
    Create a sample multi-page PDF for testing.
    
    Args:
        filename (str): Name of the PDF file to create
        num_pages (int): Number of pages to create
        
    Returns:
        Path: Path to the created PDF file
    """
    pdf_path = Path(filename)
    
    # Create a PDF with multiple pages using reportlab
    c = canvas.Canvas(str(pdf_path), pagesize=letter)
    
    for page_num in range(1, num_pages + 1):
        # Add content to each page
        c.drawString(100, 750, f"This is page {page_num} of {num_pages}")
        c.drawString(100, 700, f"Sample content for testing PDF splitter")
        c.drawString(100, 650, f"Page created: {pdf_path.name}")
        
        # Add some shapes for visual distinction
        c.rect(100, 500, 200, 100)
        c.drawString(120, 540, f"Rectangle on page {page_num}")
        
        # Start new page (except for the last page)
        if page_num < num_pages:
            c.showPage()
    
    c.save()
    print(f"Created sample PDF: {pdf_path} with {num_pages} pages")
    return pdf_path


def test_basic_splitting():
    """Test basic PDF splitting functionality."""
    print("\n=== Testing Basic PDF Splitting ===")
    
    # Create a sample PDF
    sample_pdf = create_sample_pdf("test_sample.pdf", 3)
    
    try:
        # Create output directory
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        # Test the splitter
        splitter = PDFSplitter(str(sample_pdf), str(output_dir))
        total_pages, output_files = splitter.split_pdf()
        
        print(f"✅ Successfully split {total_pages} pages")
        print(f"Output files: {[f.name for f in output_files]}")
        
        # Verify output files exist
        for output_file in output_files:
            if not output_file.exists():
                print(f"❌ Output file missing: {output_file}")
                return False
        
        print("✅ All output files created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    finally:
        # Cleanup
        if sample_pdf.exists():
            sample_pdf.unlink()


def test_error_handling():
    """Test error handling scenarios."""
    print("\n=== Testing Error Handling ===")
    
    # Test 1: Non-existent file
    try:
        splitter = PDFSplitter("nonexistent.pdf")
        splitter.split_pdf()
        print("❌ Should have failed for non-existent file")
        return False
    except PDFSplitterError as e:
        print(f"✅ Correctly handled non-existent file: {e}")
    
    # Test 2: Non-PDF file
    try:
        # Create a text file with .pdf extension
        fake_pdf = Path("fake.pdf")
        fake_pdf.write_text("This is not a PDF file")
        
        splitter = PDFSplitter(str(fake_pdf))
        splitter.split_pdf()
        print("❌ Should have failed for invalid PDF")
        return False
    except PDFSplitterError as e:
        print(f"✅ Correctly handled invalid PDF: {e}")
    finally:
        if fake_pdf.exists():
            fake_pdf.unlink()
    
    return True


def test_large_pdf():
    """Test with a larger PDF to check padding."""
    print("\n=== Testing Large PDF (Padding) ===")
    
    # Create a PDF with many pages to test padding
    sample_pdf = create_sample_pdf("test_large.pdf", 15)
    
    try:
        output_dir = Path("test_large_output")
        output_dir.mkdir(exist_ok=True)
        
        splitter = PDFSplitter(str(sample_pdf), str(output_dir))
        total_pages, output_files = splitter.split_pdf()
        
        # Check that page numbers are properly padded
        expected_names = [f"test_large_page_{i:02d}.pdf" for i in range(1, 16)]
        actual_names = [f.name for f in output_files]
        
        if actual_names == expected_names:
            print("✅ Page number padding works correctly")
            return True
        else:
            print(f"❌ Padding issue. Expected: {expected_names[:3]}...")
            print(f"❌ Got: {actual_names[:3]}...")
            return False
            
    except Exception as e:
        print(f"❌ Large PDF test failed: {e}")
        return False
    finally:
        if sample_pdf.exists():
            sample_pdf.unlink()


def cleanup_test_files():
    """Clean up test files and directories."""
    print("\n=== Cleaning Up Test Files ===")
    
    # Remove test directories
    test_dirs = ["test_output", "test_large_output"]
    for dir_name in test_dirs:
        test_dir = Path(dir_name)
        if test_dir.exists():
            # Remove all files in the directory
            for file_path in test_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
            # Remove the directory
            test_dir.rmdir()
            print(f"Cleaned up: {dir_name}")
    
    # Remove any remaining test PDFs
    test_files = ["test_sample.pdf", "test_large.pdf", "fake.pdf"]
    for file_name in test_files:
        file_path = Path(file_name)
        if file_path.exists():
            file_path.unlink()
            print(f"Cleaned up: {file_name}")


def main():
    """Run all tests."""
    print("PDF Splitter Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Splitting", test_basic_splitting),
        ("Error Handling", test_error_handling),
        ("Large PDF Padding", test_large_pdf),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    # Cleanup
    cleanup_test_files()
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
