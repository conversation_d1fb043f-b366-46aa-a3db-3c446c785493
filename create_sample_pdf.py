#!/usr/bin/env python3
"""
Create a sample multi-page PDF for demonstration purposes.
"""

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
except ImportError:
    print("Error: reportlab library is not installed.")
    print("Please install it using: pip install reportlab")
    exit(1)


def create_demo_pdf():
    """Create a sample PDF with multiple pages for demonstration."""
    filename = "sample_document.pdf"
    
    # Create a PDF with 5 pages
    c = canvas.Canvas(filename, pagesize=letter)
    
    pages_content = [
        ("Title Page", "This is the title page of our sample document"),
        ("Introduction", "This page contains the introduction to our topic"),
        ("Main Content", "Here we have the main content of the document"),
        ("Conclusion", "This page contains our conclusions and findings"),
        ("References", "Bibliography and references are listed here")
    ]
    
    for page_num, (title, content) in enumerate(pages_content, 1):
        # Page header
        c.setFont("Helvetica-Bold", 16)
        c.drawString(100, 750, f"Page {page_num}: {title}")
        
        # Page content
        c.setFont("Helvetica", 12)
        c.drawString(100, 700, content)
        c.drawString(100, 680, f"This is page {page_num} of {len(pages_content)} in the sample document.")
        
        # Add some visual elements
        c.rect(100, 500, 400, 150)
        c.drawString(120, 620, f"Sample content box for page {page_num}")
        c.drawString(120, 600, "This PDF will be split into individual pages")
        c.drawString(120, 580, "using the PDF Splitter application.")
        
        # Footer
        c.setFont("Helvetica", 10)
        c.drawString(100, 50, f"Sample Document - Page {page_num}")
        
        # Start new page (except for the last page)
        if page_num < len(pages_content):
            c.showPage()
    
    c.save()
    print(f"✅ Created sample PDF: {filename}")
    print(f"   Pages: {len(pages_content)}")
    print(f"   Ready for splitting with: python pdf_splitter.py {filename}")


if __name__ == "__main__":
    create_demo_pdf()
