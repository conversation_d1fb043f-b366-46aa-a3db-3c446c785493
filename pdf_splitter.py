#!/usr/bin/env python3
"""
PDF Splitter Application

A command-line tool to split multi-page PDF files into individual single-page PDF files.
Each page from the original PDF will be saved as a separate PDF file with a clear naming convention.

Author: PDF Splitter Tool
Version: 1.0.0
"""

import argparse
import os
import sys
from pathlib import Path
from typing import Optional, <PERSON><PERSON>

try:
    from pypdf import PdfReader, PdfWriter
except ImportError:
    print("Error: pypdf library is not installed.")
    print("Please install it using: pip install pypdf")
    sys.exit(1)


class PDFSplitterError(Exception):
    """Custom exception for PDF splitter errors."""
    pass


class PDFSplitter:
    """
    A class to handle splitting PDF files into individual pages.
    """
    
    def __init__(self, input_file: str, output_dir: Optional[str] = None):
        """
        Initialize the PDF splitter.
        
        Args:
            input_file (str): Path to the input PDF file
            output_dir (str, optional): Directory to save output files. 
                                      Defaults to same directory as input file.
        """
        self.input_file = Path(input_file)
        self.output_dir = Path(output_dir) if output_dir else self.input_file.parent
        self.base_name = self.input_file.stem
        
    def validate_input(self) -> None:
        """
        Validate the input file and output directory.
        
        Raises:
            PDFSplitterError: If validation fails
        """
        # Check if input file exists
        if not self.input_file.exists():
            raise PDFSplitterError(f"Input file not found: {self.input_file}")
        
        # Check if input file is readable
        if not os.access(self.input_file, os.R_OK):
            raise PDFSplitterError(f"No read permission for file: {self.input_file}")
        
        # Check if input file has PDF extension
        if self.input_file.suffix.lower() != '.pdf':
            raise PDFSplitterError(f"Input file must be a PDF file: {self.input_file}")
        
        # Create output directory if it doesn't exist
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            raise PDFSplitterError(f"No permission to create output directory: {self.output_dir}")
        except OSError as e:
            raise PDFSplitterError(f"Failed to create output directory: {e}")
    
    def load_pdf(self) -> PdfReader:
        """
        Load and validate the PDF file.
        
        Returns:
            PdfReader: The loaded PDF reader object
            
        Raises:
            PDFSplitterError: If PDF loading or validation fails
        """
        try:
            reader = PdfReader(str(self.input_file))
            
            # Check if PDF is encrypted
            if reader.is_encrypted:
                raise PDFSplitterError("PDF file is encrypted and cannot be processed")
            
            # Check if PDF has pages
            if len(reader.pages) == 0:
                raise PDFSplitterError("PDF file contains no pages")
            
            return reader
            
        except Exception as e:
            if isinstance(e, PDFSplitterError):
                raise
            raise PDFSplitterError(f"Failed to read PDF file: {e}")
    
    def generate_output_filename(self, page_number: int, total_pages: int) -> Path:
        """
        Generate output filename for a specific page.
        
        Args:
            page_number (int): Current page number (1-based)
            total_pages (int): Total number of pages
            
        Returns:
            Path: Output file path
        """
        # Determine padding for page numbers
        padding = len(str(total_pages))
        page_str = str(page_number).zfill(padding)
        
        filename = f"{self.base_name}_page_{page_str}.pdf"
        return self.output_dir / filename
    
    def split_pdf(self) -> Tuple[int, list]:
        """
        Split the PDF file into individual pages.
        
        Returns:
            Tuple[int, list]: Number of pages processed and list of output files
            
        Raises:
            PDFSplitterError: If splitting fails
        """
        # Validate input
        self.validate_input()
        
        # Load PDF
        reader = self.load_pdf()
        total_pages = len(reader.pages)
        
        print(f"Processing PDF: {self.input_file}")
        print(f"Total pages: {total_pages}")
        print(f"Output directory: {self.output_dir}")
        
        output_files = []
        
        try:
            for page_num in range(total_pages):
                # Create a new PDF writer for this page
                writer = PdfWriter()
                
                # Add the current page to the writer
                writer.add_page(reader.pages[page_num])
                
                # Generate output filename
                output_file = self.generate_output_filename(page_num + 1, total_pages)
                
                # Write the page to file
                try:
                    with open(output_file, 'wb') as output_pdf:
                        writer.write(output_pdf)
                    
                    output_files.append(output_file)
                    print(f"Created: {output_file.name}")
                    
                except PermissionError:
                    raise PDFSplitterError(f"No permission to write file: {output_file}")
                except OSError as e:
                    raise PDFSplitterError(f"Failed to write file {output_file}: {e}")
        
        except Exception as e:
            if isinstance(e, PDFSplitterError):
                raise
            raise PDFSplitterError(f"Error during PDF splitting: {e}")
        
        return total_pages, output_files


def main():
    """
    Main function to handle command-line interface.
    """
    parser = argparse.ArgumentParser(
        description="Split a multi-page PDF file into individual single-page PDF files.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s document.pdf
  %(prog)s /path/to/document.pdf -o /path/to/output/
  %(prog)s document.pdf --output-dir ./split_pages/
        """
    )
    
    parser.add_argument(
        'input_file',
        help='Path to the input PDF file to split'
    )
    
    parser.add_argument(
        '-o', '--output-dir',
        help='Directory to save the split PDF files (default: same as input file directory)',
        default=None
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='PDF Splitter 1.0.0'
    )
    
    args = parser.parse_args()
    
    try:
        # Create PDF splitter instance
        splitter = PDFSplitter(args.input_file, args.output_dir)
        
        # Split the PDF
        total_pages, output_files = splitter.split_pdf()
        
        # Print summary
        print(f"\n✅ Successfully split PDF into {total_pages} files!")
        print(f"Output files saved in: {splitter.output_dir}")
        
        if args.verbose:
            print("\nGenerated files:")
            for file_path in output_files:
                print(f"  - {file_path}")
    
    except PDFSplitterError as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
