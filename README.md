# PDF Splitter

A Python command-line application that splits multi-page PDF files into individual single-page PDF files.

## Features

- ✅ Split any multi-page PDF into individual single-page PDFs
- ✅ Smart file naming with zero-padded page numbers
- ✅ Comprehensive error handling for various edge cases
- ✅ Command-line interface with helpful options
- ✅ Cross-platform compatibility (Windows, macOS, Linux)
- ✅ Handles encrypted PDFs with clear error messages
- ✅ Automatic output directory creation
- ✅ Verbose mode for detailed output

## Requirements

- Python 3.6 or higher
- pypdf library

## Installation

1. **Clone or download this repository:**
   ```bash
   git clone <repository-url>
   cd pdfchaifen
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

   Or install pypdf directly:
   ```bash
   pip install pypdf
   ```

## Usage

### Basic Usage

Split a PDF file in the same directory:
```bash
python pdf_splitter.py document.pdf
```

### Advanced Usage

Specify a custom output directory:
```bash
python pdf_splitter.py document.pdf -o ./split_pages/
```

Enable verbose output:
```bash
python pdf_splitter.py document.pdf --verbose
```

### Command-line Options

```
usage: pdf_splitter.py [-h] [-o OUTPUT_DIR] [-v] [--version] input_file

Split a multi-page PDF file into individual single-page PDF files.

positional arguments:
  input_file            Path to the input PDF file to split

optional arguments:
  -h, --help            show this help message and exit
  -o OUTPUT_DIR, --output-dir OUTPUT_DIR
                        Directory to save the split PDF files (default: same as input file directory)
  -v, --verbose         Enable verbose output
  --version             show program's version number and exit
```

## Output File Naming

The application uses a smart naming convention for output files:

- **Input:** `document.pdf` (5 pages)
- **Output:** 
  - `document_page_1.pdf`
  - `document_page_2.pdf`
  - `document_page_3.pdf`
  - `document_page_4.pdf`
  - `document_page_5.pdf`

For PDFs with many pages, page numbers are zero-padded:

- **Input:** `large_document.pdf` (150 pages)
- **Output:** 
  - `large_document_page_001.pdf`
  - `large_document_page_002.pdf`
  - ...
  - `large_document_page_150.pdf`

## Error Handling

The application handles various error scenarios gracefully:

- **File not found:** Clear error message if input file doesn't exist
- **Permission errors:** Handles read/write permission issues
- **Invalid PDF files:** Detects and reports corrupted or invalid PDFs
- **Encrypted PDFs:** Reports when PDFs are password-protected
- **Empty PDFs:** Handles PDFs with no pages
- **Output directory issues:** Creates directories or reports permission problems

## Examples

### Example 1: Basic PDF Splitting
```bash
$ python pdf_splitter.py report.pdf
Processing PDF: report.pdf
Total pages: 10
Output directory: .
Created: report_page_01.pdf
Created: report_page_02.pdf
Created: report_page_03.pdf
Created: report_page_04.pdf
Created: report_page_05.pdf
Created: report_page_06.pdf
Created: report_page_07.pdf
Created: report_page_08.pdf
Created: report_page_09.pdf
Created: report_page_10.pdf

✅ Successfully split PDF into 10 files!
Output files saved in: .
```

### Example 2: Custom Output Directory
```bash
$ python pdf_splitter.py presentation.pdf -o ./slides/
Processing PDF: presentation.pdf
Total pages: 25
Output directory: slides
Created: presentation_page_01.pdf
Created: presentation_page_02.pdf
...
Created: presentation_page_25.pdf

✅ Successfully split PDF into 25 files!
Output files saved in: slides
```

### Example 3: Error Handling
```bash
$ python pdf_splitter.py nonexistent.pdf
❌ Error: Input file not found: nonexistent.pdf

$ python pdf_splitter.py encrypted.pdf
❌ Error: PDF file is encrypted and cannot be processed
```

## Project Structure

```
pdfchaifen/
├── pdf_splitter.py      # Main application file
├── requirements.txt     # Python dependencies
├── README.md           # This documentation
└── test_samples/       # Sample PDF files for testing (optional)
```

## Technical Details

### Dependencies

- **pypdf**: Modern Python library for PDF manipulation
- **argparse**: Built-in Python library for command-line argument parsing
- **pathlib**: Built-in Python library for cross-platform path handling

### Class Structure

- **PDFSplitter**: Main class handling PDF operations
  - `validate_input()`: Validates input file and output directory
  - `load_pdf()`: Loads and validates PDF file
  - `generate_output_filename()`: Creates output filenames with proper padding
  - `split_pdf()`: Main splitting logic

### Error Handling

- **PDFSplitterError**: Custom exception class for application-specific errors
- Comprehensive validation at each step
- User-friendly error messages
- Proper exit codes for scripting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Troubleshooting

### Common Issues

1. **"pypdf library is not installed"**
   - Solution: Run `pip install pypdf`

2. **Permission denied errors**
   - Solution: Check file/directory permissions or run with appropriate privileges

3. **"PDF file is encrypted"**
   - Solution: Remove password protection from the PDF first

4. **Memory issues with large PDFs**
   - Solution: The application processes pages one at a time to minimize memory usage

### Getting Help

If you encounter issues:
1. Check the error message for specific guidance
2. Verify your Python version (3.6+ required)
3. Ensure all dependencies are installed
4. Check file permissions and paths
